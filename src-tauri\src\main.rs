// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]
mod api;
mod commands;
mod config;
mod utils;

use crate::config::app_config::Settings;
use api::{explorer_handle, hotkey_listener};
use commands::core_commands::set_window_acrylic_impl;
use commands::{
    config_commands, explorer_commands, hotkey_commands, ollama_commands, startup_commands,
};
use config::app_config::DebugMode;
use core::time;
use rcgen::RsaKeySize;
use rcgen::{CertificateParams, KeyPair, PKCS_RSA_SHA256};
use std::error::Error;
use std::fs::File;
use std::io::Write;
use std::process::Child;
use std::thread::sleep;
use std::time::{Duration, Instant};
use std::{os::windows::process::CommandExt, path::Path};
use tauri::{App, Manager};
use tauri_plugin_global_shortcut::{GlobalShortcutExt, Shortcut, ShortcutState};
use tauri_plugin_prevent_default::Flags;
use utils::{bat_vbs, cert, jvm, round_corner};

// const MAIN_QUERY_WIDTH: f64 = 800.0;

const EXIT_SIGNAL_FILE_NAME: &str = "$$_aiverything_exit_signal";
fn main() {
    tauri::Builder::default()
        .plugin(tauri_plugin_updater::Builder::new().build())
        .plugin(tauri_plugin_os::init())
        .plugin(tauri_plugin_single_instance::init(|app, argv, cwd| {
            println!("{}, {argv:?}, {cwd}", app.package_info().name);
            // 这里会导致网页登录账号，点击打开Aiverything后弹出搜索框
            // app.get_webview_window("main").unwrap().show().unwrap();
        }))
        .plugin(tauri_plugin_deep_link::init())
        .plugin(tauri_plugin_notification::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_process::init())
        .plugin(
            tauri_plugin_prevent_default::Builder::new()
                .with_flags(
                    Flags::FIND
                        | Flags::CARET_BROWSING
                        | Flags::OPEN
                        | Flags::PRINT
                        | Flags::DOWNLOADS,
                )
                .build(),
        )
        .setup(move |app| {
            if cfg!(debug_assertions) {
                use tauri_plugin_deep_link::DeepLinkExt;
                app.deep_link().register_all()?;
            }

            // 设置panic hook
            // std::panic::set_hook(Box::new(|_| {
            //     let _ = pre_exit();
            // }));

            // 设置Ctrl+C处理
            ctrlc::set_handler(move || {
                let _ = pre_exit();
                std::process::exit(0);
            })
            .expect("Error setting Ctrl-C handler");

            // generate trusted root ca certificate
            generate_ca().expect("Failed to generate CA certificate");

            // make sure all service exited
            generate_exit_signal_file().unwrap_or_else(|e| {
                eprintln!("Failed to generate exit signal file: {}", e);
            });

            // let _ = utils::startup_util::generate_startup_vbs();

            let one_second = time::Duration::from_secs(1);
            sleep(one_second);

            // delete exit signal file
            delete_exit_signal_file().expect("Failed to delete exit signal file");

            if cfg!(debug_assertions) {
                let current_working_dir =
                    std::env::current_dir().expect("Failed to get current dir");
                let core_path_to_extract = current_working_dir.join("../core");
                //将src-tauri/core文件夹复制到../core
                copy_dir_all(current_working_dir.join("core"), &core_path_to_extract)
                    .expect("Failed to copy core dir");
            }

            let app_configs = config::app_config::read_config().expect("Failed to read config");
            let debug_mode = app_configs.debug_mode;

            // start core
            let core_process = start_core().expect("Failed to start core process");
            start_daemon(core_process, start_core).expect("Failed to start core daemon");

            // start plugin service
            start_plugin_service(debug_mode).expect("Failed to start plugin process");

            explorer_handle::start().expect("Failed to start explorer monitor thread");

            hotkey_listener::start_listen().expect("Failed to start hotkey listener");
            let app_configs = config::app_config::read_config().expect("Failed to read config");

            explorer_commands::start_monitor_explorer(app);

            let window = app.get_webview_window("main").unwrap();

            // set acrylic window
            set_window_acrylic_impl(&window);

            // set round corner
            round_corner::set_window_round_corner(&window)
                .expect("Failed to set window round corner");

            commands::core_commands::set_main_window(window);
            // register tray icon and menu
            // register_tray(app)?;

            // register global hotkey
            register_hotkey(app, app_configs)
        })
        .on_window_event(|window, event| match event {
            tauri::WindowEvent::Focused(focused) => {
                // hide window whenever it loses focus
                if !cfg!(debug_assertions) {
                    if !focused {
                        if window.label() == "main" {
                            if let (Ok(cursor_pos), Ok(window_pos)) =
                                (window.cursor_position(), window.outer_position())
                            {
                                // Check if cursor is in the bottom-right area
                                let is_in_window = cursor_pos.x
                                    <= window_pos.x as f64
                                        + window.inner_size().unwrap().width as f64
                                    && cursor_pos.y
                                        <= window_pos.y as f64
                                            + window.inner_size().unwrap().height as f64
                                    && cursor_pos.x >= window_pos.x as f64
                                    && cursor_pos.y >= window_pos.y as f64;
                                println!(
                                    "cursor_pos: {:?}, window_pos: {:?}, is_in_bottom_right: {}",
                                    cursor_pos, window_pos, is_in_window
                                );
                                if !is_in_window {
                                    window.hide().unwrap();
                                }
                            }
                        }
                    }
                }
            }
            _ => {}
        })
        .invoke_handler(tauri::generate_handler![
            commands::core_commands::get_icon,
            commands::core_commands::get_config,
            commands::core_commands::get_gpu,
            commands::core_commands::get_disk,
            commands::core_commands::set_config,
            commands::core_commands::close,
            commands::core_commands::status,
            commands::core_commands::flush_file_changes,
            commands::core_commands::get_frequent_result,
            commands::core_commands::prepare_search,
            commands::core_commands::search_async,
            commands::core_commands::search_ai,
            commands::core_commands::summarize_ai,
            commands::core_commands::remove_summary,
            commands::core_commands::get_summary,
            commands::core_commands::stop_search,
            commands::core_commands::get_result,
            commands::core_commands::get_uwp_result,
            commands::core_commands::remove_result,
            commands::core_commands::add_cache,
            commands::core_commands::get_cache,
            commands::core_commands::delete_cache,
            commands::core_commands::update,
            commands::core_commands::start_new_program_without_admin,
            commands::core_commands::start_new_program_with_admin,
            commands::core_commands::version,
            commands::core_commands::build_version,
            commands::core_commands::get_window_acrylic_state,
            commands::core_commands::apply_window_acrylic,
            commands::core_commands::clear_window_acrylic,
            commands::core_commands::get_current_core_port,
            commands::plugin_commands::get_plugin_list,
            commands::plugin_commands::get_plugin_info,
            commands::plugin_commands::get_plugin_resource_url,
            commands::plugin_commands::get_plugin_config_value,
            commands::plugin_commands::get_plugin_config_raw,
            commands::plugin_commands::get_plugin_config_file_path,
            commands::plugin_commands::get_plugin_load_error,
            commands::plugin_commands::plugin_enter,
            commands::plugin_commands::plugin_exit,
            startup_commands::has_startup,
            startup_commands::add_startup,
            startup_commands::delete_startup,
            config_commands::read_app_config,
            config_commands::set_app_config,
            explorer_commands::get_calculated_window_info,
            explorer_commands::set_explorer_edit_path,
            explorer_commands::bring_window_to_top,
            hotkey_commands::is_ctrl_key_double_clicked,
            hotkey_commands::is_shift_key_double_clicked,
            hotkey_commands::get_async_key_state,
            hotkey_commands::get_swap_button_state,
            commands::core_commands::run_uwp,
            ollama_commands::get_model_list,
            pre_exit,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

fn register_hotkey(app: &mut App, app_configs: Settings) -> Result<(), Box<dyn Error>> {
    let app_hotkey_config = app_configs.hotkey;
    let app_shortcut = Shortcut::new(Some(app_hotkey_config.modifiers), app_hotkey_config.key);
    let app_handle = app.handle();

    // 处理双击 Ctrl
    let handle = app_handle.clone();

    let mut window_switch_time = Instant::now();
    let wait_timeout = Duration::from_millis(200);
    std::thread::spawn(move || loop {
        let thread_app_configs = config::app_config::read_config().expect("Failed to read config");
        let enable_double_ctrl = thread_app_configs.enable_double_ctrl;
        // 切换窗口状态后 wait_timeout 内不允许再次切换，防止Ctrl双击过快导致窗口闪烁
        if enable_double_ctrl && window_switch_time + wait_timeout < Instant::now() {
            if let Ok(result) = hotkey_listener::is_ctrl_key_double_clicked() {
                if result > 0 {
                    let focused = handle
                        .get_webview_window("main")
                        .unwrap()
                        .is_visible()
                        .unwrap();
                    if focused {
                        handle.get_webview_window("main").unwrap().hide().unwrap();
                    } else {
                        handle.get_webview_window("main").unwrap().show().unwrap();
                        handle
                            .get_webview_window("main")
                            .unwrap()
                            .set_focus()
                            .unwrap();
                    }

                    window_switch_time = Instant::now();
                }
            }
        }
        sleep(Duration::from_millis(100));
    });

    app.handle().plugin(
        tauri_plugin_global_shortcut::Builder::new()
            .with_handler(move |_app, shortcut, event| {
                println!("{:?}", shortcut);
                if shortcut == &app_shortcut {
                    match event.state() {
                        ShortcutState::Pressed => {
                            let focused = _app
                                .get_webview_window("main")
                                .unwrap()
                                .is_visible()
                                .unwrap();
                            if focused {
                                _app.get_webview_window("main").unwrap().hide().unwrap();
                            } else {
                                _app.get_webview_window("main").unwrap().show().unwrap();
                                _app.get_webview_window("main")
                                    .unwrap()
                                    .set_focus()
                                    .unwrap();
                            }
                        }

                        ShortcutState::Released => {
                            println!("Short Cut Released!");
                        }
                    }
                }
            })
            .build(),
    )?;
    app.global_shortcut().register(app_shortcut)?;
    Ok(())
}

fn start_daemon(
    mut proc: Child,
    start_proc_fn: fn() -> Result<Child, Box<dyn Error>>,
) -> Result<(), Box<dyn Error>> {
    std::thread::spawn(move || {
        loop {
            match proc.try_wait() {
                Ok(Some(status)) => {
                    println!("Process exited with status: {:?}", status);
                    println!("Restarting process...");
                    proc = start_proc_fn().expect("failed to start process");
                }
                Ok(None) => {
                    // 子进程仍在运行
                    sleep(Duration::from_secs(1));
                }
                Err(e) => {
                    eprintln!("Failed to check process status: {:?}", e);
                    break;
                }
            }
        }
    });
    Ok(())
}

fn start_plugin_service(debug_mode: DebugMode) -> Result<(), Box<dyn Error>> {
    match std::env::current_dir() {
        Ok(current_working_dir) => {
            let plugin_path_to_extract;
            if cfg!(debug_assertions) {
                plugin_path_to_extract = current_working_dir.join("../core");
            } else {
                plugin_path_to_extract = current_working_dir.join("core");
            }
            if !plugin_path_to_extract.exists() {
                std::fs::create_dir_all(&plugin_path_to_extract)?;
            }
            let plugin_service_jar_name = "Plugin-Service.jar";

            let plugin_service_jar_path = plugin_path_to_extract.join(plugin_service_jar_name);

            let java_exe = plugin_path_to_extract.join("jre/bin/java.exe");
            let jvm_options_file_path = plugin_path_to_extract
                .canonicalize()?
                .join("pluginjvm.vmoptions");

            if !plugin_service_jar_path.exists() {
                //抛出异常
                panic!("Plugin service jar does not exist");
            }

            let mut jvm_params =
                jvm::init_default_jvm_parameters(jvm_options_file_path.to_str().unwrap())?;

            if debug_mode.enable {
                jvm_params.push(
                    "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:35005"
                        .to_string(),
                );
                jvm_params.push("-Daiverything.debug=true".to_string());
                if debug_mode.java_agent != "" {
                    jvm_params.push(format!("-javaagent:\"{}\"", debug_mode.java_agent));
                }
            }

            let java_exe_path = java_exe.canonicalize()?;
            let java_exe_path_string;
            if debug_mode.enable {
                java_exe_path_string = debug_mode.jdk_home + "/bin/java.exe";
            } else {
                java_exe_path_string = bat_vbs::adjust_canonicalization(java_exe_path);
            }

            // 在java_exe_path_string第二个字符前和最后加上双引号，防止路径有空格
            let java_exe_path_with_quotes = format!(
                "{}\"{}\"",
                &java_exe_path_string[0..1],
                &java_exe_path_string[1..]
            );
            let start_command = java_exe_path_with_quotes
                + " "
                + jvm_params.join(" ").as_str()
                + " -jar "
                + plugin_service_jar_name;

            let working_dir_path =
                bat_vbs::adjust_canonicalization(plugin_path_to_extract.canonicalize()?);

            // Generate bat file contains command in temp folder
            let temp_folder = std::env::temp_dir();
            // Generate random file name
            let file_name = "$$_aiverything_open_plugin_service.bat";
            let bat_file_path = temp_folder.join(file_name);

            bat_vbs::generate_open_file_bat(
                &start_command,
                working_dir_path.as_str(),
                bat_file_path.to_owned(),
            )?;

            let bat_file_path_str = bat_vbs::adjust_canonicalization(bat_file_path.canonicalize()?);

            // Generate vbs file contains command in temp folder
            let temp_folder = std::env::temp_dir();
            // Generate random file name
            let file_name = "$$_aiverything_open_plugin_service.vbs";
            let vbs_file_path = temp_folder.join(file_name);

            bat_vbs::generate_open_file_vbs(bat_file_path_str.as_str(), vbs_file_path.to_owned())?;

            if plugin_path_to_extract.starts_with("C:\\") {
                std::process::Command::new("cmd.exe")
                    .creation_flags(0x08000000)
                    .current_dir(&plugin_path_to_extract)
                    .arg("/C")
                    .arg(vbs_file_path)
                    .spawn()
                    .expect("failed to execute process");
            } else {
                std::process::Command::new("explorer")
                    .creation_flags(0x08000000)
                    .current_dir(&plugin_path_to_extract)
                    .arg(vbs_file_path)
                    .spawn()
                    .expect("failed to execute process");
            }
            Ok(())
        }
        Err(e) => {
            eprintln!("Failed to get current working directory: {}", e);
            Err("Failed to get current working directory")?
        }
    }
}

fn start_core() -> Result<Child, Box<dyn Error>> {
    match std::env::current_dir() {
        Ok(current_working_dir) => {
            let core_path_to_extract;
            if cfg!(debug_assertions) {
                core_path_to_extract = current_working_dir.join("../core");
            } else {
                core_path_to_extract = current_working_dir.join("core");
            }
            if !core_path_to_extract.exists() {
                std::fs::create_dir_all(&core_path_to_extract)?;
            }
            let file_engine_jar_name = "File-Engine-Core.jar";

            let file_engine_jar_path = core_path_to_extract.join(file_engine_jar_name);

            let java_exe = core_path_to_extract.join("jre/bin/java.exe");
            let jvm_options_file_path = core_path_to_extract.canonicalize()?.join("jvm.vmoptions");

            if !file_engine_jar_path.exists() {
                //抛出异常
                panic!("File Engine Core not found");
            }

            let jvm_params =
                jvm::init_default_jvm_parameters(jvm_options_file_path.to_str().unwrap())?;

            let java_exe_path = java_exe.canonicalize()?;

            let log_path = core_path_to_extract.join("file-engine-core.log");

            let log_file = std::fs::OpenOptions::new()
                .create(true)
                .append(true)
                .open(&log_path)?;

            let core_port = utils::port_manager::get_core_port()?;

            let core_process = std::process::Command::new(&java_exe_path)
                .stderr(log_file)
                .creation_flags(0x08000000)
                .current_dir(&core_path_to_extract)
                .args(&jvm_params)
                .args(&["-jar", &file_engine_jar_name, &core_port.to_string()])
                .spawn()
                .expect("failed to execute process");
            Ok(core_process)
        }
        Err(e) => {
            eprintln!("Failed to get current working directory: {}", e);
            Err("Failed to get current working directory")?
        }
    }
}

fn copy_dir_all(src: impl AsRef<Path>, dst: impl AsRef<Path>) -> std::io::Result<()> {
    std::fs::create_dir_all(&dst)?;
    for entry in std::fs::read_dir(src)? {
        let entry = entry?;
        let ty = entry.file_type()?;
        if ty.is_dir() {
            copy_dir_all(entry.path(), dst.as_ref().join(entry.file_name()))?;
        } else {
            std::fs::copy(entry.path(), dst.as_ref().join(entry.file_name()))?;
        }
    }
    Ok(())
}

fn delete_exit_signal_file() -> Result<(), Box<dyn Error>> {
    let temp_folder = std::env::temp_dir();
    let exit_signal_file_path = temp_folder.join(EXIT_SIGNAL_FILE_NAME);
    if exit_signal_file_path.exists() {
        std::fs::remove_file(&exit_signal_file_path)?;
    }
    Ok(())
}

fn generate_exit_signal_file() -> Result<(), Box<dyn Error>> {
    let temp_folder = std::env::temp_dir();
    let exit_signal_file_path = temp_folder.join(EXIT_SIGNAL_FILE_NAME);
    File::create(&exit_signal_file_path)?;
    Ok(())
}

fn generate_ca() -> Result<(), Box<dyn Error>> {
    // 获取windows AppData文件夹路径
    let appdata_path = std::env::var("APPDATA").unwrap();

    let aiverything_path = Path::new(&appdata_path).join("aiverything");
    if !aiverything_path.exists() {
        std::fs::create_dir_all(&aiverything_path)?;
    }

    let cer_file_name = "ca.cer";
    let key_file_name = "ca.key";
    let cer_file = Path::new(&aiverything_path).join(cer_file_name);
    let key_file = Path::new(&aiverything_path).join(key_file_name);

    if cer_file.exists() && key_file.exists() {
        let ret_import = cert::import_certificate(cer_file.canonicalize()?.to_str().unwrap());
        match ret_import {
            Ok(_) => {
                return Ok(());
            }
            Err(e) => {
                eprintln!("Failed to import CA certificate, Error {}", e);
                return Err(e.into());
            }
        }
    }

    let subject_alt_names = vec!["localhost".to_string(), "*.aiverything.me".to_string()];
    let key_pair = KeyPair::generate_rsa_for(&PKCS_RSA_SHA256, RsaKeySize::_2048)?;
    let cert = CertificateParams::new(subject_alt_names)?.self_signed(&key_pair)?;
    let root_ca = cert.pem();
    let key = key_pair.serialize_pem();

    {
        let mut cer_output_stream = File::create(&cer_file)?;
        write!(cer_output_stream, "{}", root_ca)?;

        let mut key_output_stream = File::create(&key_file)?;
        write!(key_output_stream, "{}", key)?;
    }

    let ret_import = cert::import_certificate(cer_file.canonicalize()?.to_str().unwrap());
    match ret_import {
        Ok(_) => {
            return Ok(());
        }
        Err(e) => {
            eprintln!("Failed to import CA certificate, Error {}", e);
            return Err(e.into());
        }
    }
}

#[tauri::command]
fn pre_exit() -> Result<(), String> {
    generate_exit_signal_file().expect("Failed to generate exit signal file");
    explorer_handle::stop().expect("Failed to stop explorer monitor thread");
    hotkey_listener::stop_listen().expect("Failed to stop hotkey listener");
    Ok(())
}
