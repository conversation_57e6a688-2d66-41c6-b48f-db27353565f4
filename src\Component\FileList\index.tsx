import React, { useState, useEffect, useRef, useLayoutEffect } from "react";
import { useSelector } from "react-redux";
import { selectInputValue } from "../../slices/inputValueSlice";
import {
  selectFlatMode,
  selectSortBy,
  selectSortOrder,
} from "../../slices/searchResultSlice";
import FileIcon from "../FileIcon";
import { Data, DataType } from "../MainAnwser";
import { getCurrentWindow } from "@tauri-apps/api/window";
import { open } from "@tauri-apps/plugin-shell";
import {
  openFileWithoutAdmin,
  openFileWithAdmin,
  showItemInFolder,
} from "../../api/system";
import {
  addCache,
  readAppConfig,
  runUwp,
  getConfig,
  highlight,
} from "../../api/aiverything";
import {
  sendNotification,
  isPermissionGranted,
  requestPermission,
} from "@tauri-apps/plugin-notification";
import { useTranslation } from "react-i18next";

import { FaGoogle } from "react-icons/fa";
import { BsBing } from "react-icons/bs";
import { SiDuckduckgo, SiBaidu } from "react-icons/si";
import CustomScrollbar from "../CustomScrollbar";



const formatFileSize = (size: number) => {
  if (size < 1024) return `${size} B`;
  const i = Math.floor(Math.log(size) / Math.log(1024));
  const sizes = ["B", "KB", "MB", "GB", "TB"];
  return `${(size / Math.pow(1024, i)).toFixed(2)} ${sizes[i]}`;
};

// 渲染搜索引擎图标
const renderSearchEngineIcon = (searchEngineName: string) => {
  switch (searchEngineName) {
    case "Baidu":
      return <SiBaidu className="w-full h-full object-contain" />;
    case "Bing":
      return <BsBing className="w-full h-full object-contain" />;
    case "DuckDuckGo":
      return <SiDuckduckgo className="w-full h-full object-contain" />;
    default:
      return <FaGoogle className="w-full h-full object-contain" />;
  }
};

// 高亮显示组件
const HighlightedText: React.FC<{ text: string; className?: string }> = ({
  text,
  className,
}) => {
  if (!text) return null;

  // 解析包含<highlight></highlight>标签的文本
  const parts = text.split(/(<highlight>.*?<\/highlight>)/g);

  return (
    <span className={className}>
      {parts.map((part, index) => {
        if (part.startsWith("<highlight>") && part.endsWith("</highlight>")) {
          // 移除标签，只保留内容
          const content = part.slice(11, -12); // 移除<highlight>和</highlight>
          return (
            <span
              key={index}
              className="bg-yellow-200 dark:bg-yellow-600 px-0.5 rounded text-black dark:text-white font-semibold"
            >
              {content}
            </span>
          );
        }
        return <span key={index}>{part}</span>;
      })}
    </span>
  );
};

const FileListItem = ({
  file,
  t,
  isRightPanelExpanded,
  searchUuid,
  onHighlightDataLoaded,
}) => {
    const [localHighlightData, setLocalHighlightData] = useState<{
      fileName?: string;
      parentPath?: string;
    } | null>(null);
    const hasRequestedRef = useRef(false);

    // 计算父目录路径的辅助函数
    const getParentPath = (filePath: string) => {
      const lastBackslash = filePath.lastIndexOf("\\");
      const lastForwardslash = filePath.lastIndexOf("/");
      const lastSeparatorIndex = Math.max(lastBackslash, lastForwardslash);
      return lastSeparatorIndex > 0
        ? filePath.substring(0, lastSeparatorIndex)
        : filePath;
    };

    // 高亮数据加载函数
    const loadHighlightForFile = async () => {
      if (hasRequestedRef.current) return;

      const filePath = file.path;
      const fileName = file.name;
      const parentPath = getParentPath(file.path);

      if (!filePath || !fileName || !parentPath || !searchUuid) return;

      // 检查是否需要高亮
      if (
        file.highlightFileName ||
        file.highlightPath ||
        file.type === DataType.SearchOnWeb ||
        file.type === DataType.UwpApp
      ) {
        return;
      }

      hasRequestedRef.current = true;

      try {
        let fileNameProcessed = fileName;
        if (fileName?.endsWith(".lnk")) {
          fileNameProcessed = fileName.slice(0, -4);
        }
        const response = await highlight(
          fileNameProcessed,
          parentPath,
          searchUuid
        );
        const result = await response.json();

        if (result.status === 200 && result.data) {
          const highlightData = {
            fileName: result.data.fileName,
            parentPath: result.data.parentPath,
          };
          setLocalHighlightData(highlightData);
          // 通知父组件更新数据
          if (onHighlightDataLoaded) {
            onHighlightDataLoaded(filePath, highlightData);
          }
        }
      } catch (error) {
        console.error("Failed to load highlight data:", error);
      }
    };

    // 在组件创建时直接请求高亮数据
    useEffect(() => {
      loadHighlightForFile();
    }, [file.path, file.name, searchUuid]); // 依赖文件路径、名称和搜索UUID

    // 重置请求状态当searchUuid变化时
    useEffect(() => {
      hasRequestedRef.current = false;
      setLocalHighlightData(null);
    }, [searchUuid]);

    // 优先使用本地异步加载的高亮数据，然后是文件原有的高亮数据
    const highlightFileName =
      localHighlightData?.fileName || file.highlightFileName;
    const highlightPath = localHighlightData?.parentPath || file.highlightPath;

    // 处理快捷方式文件名，去掉.lnk后缀
    const getDisplayFileName = (fileName: string) => {
      if (fileName.toLowerCase().endsWith(".lnk")) {
        return fileName.slice(0, -4); // 去掉最后4个字符(.lnk)
      }
      return fileName;
    };

    switch (file.type) {
      case DataType.SearchOnWeb:
        return (
          <React.Fragment>
            <div className="w-9 h-12 mr-4">
              {renderSearchEngineIcon(file.metaData?.searchEngine || "Google")}
            </div>
            <div className="flex-1 min-w-0">
              <div className="font-semibold truncate">{file.name}</div>
            </div>
          </React.Fragment>
        );
      case DataType.UwpApp:
        return (
          <React.Fragment>
            <FileIcon filepath={file.path} target={"List"} isUwp={true} />
            <div
              className={`flex-1 min-w-0 ${
                isRightPanelExpanded ? "max-w-32" : "max-w-96"
              }`}
            >
              <div className="font-semibold truncate">
                {highlightFileName ? (
                  <HighlightedText text={highlightFileName} />
                ) : (
                  file.name
                )}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400 truncate">
                UWP Application
              </div>
              <div className="truncate text-xs text-gray-600 dark:text-gray-500 mt-0.5 font-medium">
                {isRightPanelExpanded
                  ? file.metaData.fullName?.split(".").pop() ||
                    file.metaData.fullName
                  : file.metaData.fullName}
              </div>
            </div>
          </React.Fragment>
        );
      default:
        return (
          <React.Fragment>
            <FileIcon filepath={file.path} target={"List"} />
            <div
              className={`flex-1 min-w-0 ${
                isRightPanelExpanded ? "max-w-32" : "max-w-96"
              }`}
            >
              <div className="font-semibold truncate">
                {highlightFileName ? (
                  <HighlightedText text={highlightFileName} />
                ) : (
                  getDisplayFileName(file.name)
                )}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400 truncate">
                {formatFileSize(file.size.number)} ·{" "}
                {t("searchBar.detail.lastModified")}{" "}
                {file.lastModified?.toLocaleString()}
              </div>
              <div className="truncate text-xs text-gray-600 dark:text-gray-500 mt-0.5 font-medium">
                {highlightPath ? (
                  <HighlightedText
                    text={
                      isRightPanelExpanded
                        ? highlightPath.split("\\").pop() || highlightPath
                        : highlightPath
                    }
                    className="break-all"
                  />
                ) : isRightPanelExpanded ? (
                  file.path.split("\\").pop() ||
                  file.path.split("/").pop() ||
                  file.path
                ) : (
                  file.path
                )}
              </div>
            </div>
          </React.Fragment>
        );
    }
  }
};

const FileList = ({
  data,
  onSelect,
  isRightPanelExpanded = false,
  searchUuid,
}) => {
  const [selectedFile, setSelectedFile] = useState<Data>();
  const [selectedIndex, setSelectedIndex] = useState({ section: 0, file: 0 });
  const [lastAction, setLastAction] = useState("keyboard");
  const [dataTypeSuffixMap, setDataTypeSuffixMap] = useState<
    {
      dataType: string;
      suffix: string[];
    }[]
  >([]);

  // 添加状态来存储异步加载的高亮数据 - 现在由FileListItem内部管理
  const [asyncHighlightData, setAsyncHighlightData] = useState<{
    [key: string]: { fileName?: string; parentPath?: string };
  }>({});

  const [isScrolling, setIsScrolling] = useState(false);
  const [isResettingSelection, setIsResettingSelection] = useState(false);
  const [contextMenu, setContextMenu] = useState<{
    visible: boolean;
    x: number;
    y: number;
    file: Data | null;
  }>({
    visible: false,
    x: 0,
    y: 0,
    file: null,
  });
  const scrollTimeoutRef = useRef<number | null>(null);
  const inputValue = useSelector(selectInputValue);
  const flatMode = useSelector(selectFlatMode);
  const sortBy = useSelector(selectSortBy);
  const sortOrder = useSelector(selectSortOrder);
  const selectedFileRef = useRef(null);
  const containerRef = useRef(null);
  const sectionRefs = useRef({});
  const { t } = useTranslation();

  // 加载dataTypeSuffixMap配置
  useEffect(() => {
    const loadDataTypeSuffixMap = async () => {
      try {
        const config = await getConfig();
        if (
          config &&
          config.data &&
          config.data.dataTypeSuffixMap &&
          config.data.dataTypeSuffixMap.length > 0
        ) {
          setDataTypeSuffixMap(config.data.dataTypeSuffixMap);
          return true; // 加载成功
        }
      } catch (error) {
        console.error("Failed to load dataTypeSuffixMap config:", error);
      }
      return false; // 加载失败或为空
    };

    const retryLoad = async () => {
      while (true) {
        const success = await loadDataTypeSuffixMap();
        if (success) {
          console.log("dataTypeSuffixMap loaded successfully");
          break;
        }
        console.log("dataTypeSuffixMap is empty, retrying in 1 second...");
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
    };

    retryLoad();
  }, []);

  // 根据后缀获取优先级
  const getSuffixPriority = (extension: string): number => {
    if (!extension) return -10; // 没有后缀的文件优先级最低
    const ext = extension.toLowerCase().replace(".", "");

    // 遍历 dataTypeSuffixMap 查找后缀
    for (
      let dataTypeIndex = 0;
      dataTypeIndex < dataTypeSuffixMap.length;
      dataTypeIndex++
    ) {
      const dataType = dataTypeSuffixMap[dataTypeIndex];
      const suffixIndex = dataType.suffix.findIndex(
        (suffix) => suffix.toLowerCase() === ext
      );

      if (suffixIndex !== -1) {
        // 计算优先级：越靠前的优先级越高（返回值越大）
        // 基础优先级 = 总长度 - dataType索引，确保第一个dataType优先级最高
        // 后缀优先级 = 该dataType中后缀总数 - 后缀索引，确保第一个后缀优先级最高
        const baseDataTypePriority =
          (dataTypeSuffixMap.length - dataTypeIndex) * 1000;
        const suffixPriority = dataType.suffix.length - suffixIndex;
        return baseDataTypePriority + suffixPriority;
      }
    }

    return 0; // 未配置的后缀优先级最低
  };

  // 排序函数
  const sortData = (
    data: Data[],
    sortBy: string,
    sortOrder: "asc" | "desc"
  ) => {
    if (sortBy === "relevance") {
      return data; // 相关性排序不改变顺序
    }

    return [...data].sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortBy) {
        case "lastModified":
          aValue = a.lastModified ? new Date(a.lastModified).getTime() : 0;
          bValue = b.lastModified ? new Date(b.lastModified).getTime() : 0;
          // UWP应用没有lastModified时间，给它们一个默认的最新时间
          if (a.type === DataType.UwpApp) aValue = Date.now();
          if (b.type === DataType.UwpApp) bValue = Date.now();
          break;
        case "name":
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case "type":
          // UWP应用设置为最高优先级
          aValue =
            a.type === DataType.UwpApp
              ? 999999
              : getSuffixPriority(a.extension);
          bValue =
            b.type === DataType.UwpApp
              ? 999999
              : getSuffixPriority(b.extension);
          break;
        case "fileSize":
          aValue = a.size?.number || 0;
          bValue = b.size?.number || 0;
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return sortOrder === "asc" ? -1 : 1;
      if (aValue > bValue) return sortOrder === "asc" ? 1 : -1;
      return 0;
    });
  };

  // 根据flatMode处理数据
  const processedData = React.useMemo(() => {
    if (!data || data.length === 0) return [];

    if (flatMode) {
      // 平铺模式：将所有section的数据合并成一个数组
      const allFiles = data.reduce((acc, section) => {
        if (section && section.data) {
          return [...acc, ...section.data];
        }
        return acc;
      }, []);

      // 对合并后的数据进行排序
      const sortedAllFiles = sortData(allFiles, sortBy, sortOrder);

      // 创建一个虚拟的section包含所有文件
      return [
        {
          datatype: "All Files",
          data: sortedAllFiles,
        },
      ];
    }

    // 正常分组模式 - 对每个section的数据进行排序
    return data.map((section) => {
      if (section && section.data) {
        return {
          ...section,
          data: sortData(section.data, sortBy, sortOrder),
        };
      }
      return section;
    });
  }, [data, flatMode, sortBy, sortOrder]);

  // 发送系统通知函数
  const sendSystemNotification = async (title, body) => {
    let permissionGranted = await isPermissionGranted();
    if (!permissionGranted) {
      const permission = await requestPermission();
      permissionGranted = permission === "granted";
    }
    if (permissionGranted) {
      sendNotification({ title, body });
    }
  };

  // 处理右键菜单选项
  const handleContextMenuAction = async (action: string, file: Data) => {
    if (!file) return;

    switch (action) {
      case "open":
        if (file.type === DataType.UwpApp && file.appUserModelId) {
          try {
            await runUwp(file.appUserModelId);
          } catch (error) {
            console.error("Failed to launch UWP app:", error);
          }
        } else if (file.path) {
          addCache(file.path);
          openFileWithoutAdmin(file.path);
        }
        break;
      case "copy":
        const pathToCopy =
          file.type === DataType.UwpApp
            ? file.metaData?.fullName || file.name
            : file.path;
        if (pathToCopy) {
          if (file.path) addCache(file.path);
          navigator.clipboard.writeText(pathToCopy);
          sendSystemNotification("Aiverything", t("searchBar.filePathCopied"));
        }
        break;
      case "openParent":
        if (file.path) {
          addCache(file.path);
          showItemInFolder(file.path);
        }
        break;
      case "openAdmin":
        if (file.path) {
          addCache(file.path);
          openFileWithAdmin(file.path);
        }
        break;
    }
    getCurrentWindow().hide();
    setContextMenu({ visible: false, x: 0, y: 0, file: null });
  };

  // 处理右键点击
  const handleContextMenu = (e: React.MouseEvent, file: Data) => {
    e.preventDefault();
    e.stopPropagation();

    // 排除网络搜索类型，其他类型都可以显示右键菜单
    if (file.type === DataType.SearchOnWeb) {
      return;
    }

    setContextMenu({
      visible: true,
      x: e.clientX,
      y: e.clientY,
      file: file,
    });

    // 同时选中这个文件
    const sectionIndex = processedData.findIndex((section) =>
      section.data.some((item) => item.path === file.path)
    );
    const fileIndex =
      processedData[sectionIndex]?.data.findIndex(
        (item) => item.path === file.path
      ) ?? 0;

    setSelectedFile(file);
    onSelect(file);
    setSelectedIndex({ section: sectionIndex, file: fileIndex });
  };

  // 点击其他地方关闭右键菜单
  useEffect(() => {
    const handleClickOutside = () => {
      setContextMenu({ visible: false, x: 0, y: 0, file: null });
    };

    if (contextMenu.visible) {
      document.addEventListener("click", handleClickOutside);
      document.addEventListener("contextmenu", handleClickOutside);
    }

    return () => {
      document.removeEventListener("click", handleClickOutside);
      document.removeEventListener("contextmenu", handleClickOutside);
    };
  }, [contextMenu.visible]);

  // 初始化 section refs
  useEffect(() => {
    if (processedData) {
      sectionRefs.current = processedData.reduce((acc, _, index) => {
        acc[index] = React.createRef();
        return acc;
      }, {});
    }
  }, [processedData]);

  const scrollToSection = (sectionIndex) => {
    if (sectionRefs.current[sectionIndex]?.current) {
      // 设置滚动状态为 true
      setIsScrolling(true);

      // 清除之前的超时
      if (scrollTimeoutRef.current !== null) {
        clearTimeout(scrollTimeoutRef.current);
      }

      sectionRefs.current[sectionIndex].current.scrollIntoView({
        behavior: "smooth",
        block: "nearest",
      });

      // 选择该部分的第一个文件
      setSelectedIndex({ section: sectionIndex, file: 0 });

      // 设置一个超时，在滚动完成后重置滚动状态
      // 通常滚动动画大约需要 500-1000ms
      scrollTimeoutRef.current = window.setTimeout(() => {
        setIsScrolling(false);
        scrollTimeoutRef.current = null;
      }, 1000);
    }
  };

  useLayoutEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === "Alt") {
        event.preventDefault();
      }
      setLastAction("keyboard");
      if (event.key === "ArrowDown") {
        setSelectedIndex((prevIndex) => {
          const newIndex = { ...prevIndex };
          if (!processedData?.[newIndex.section]?.data) {
            return newIndex;
          }
          if (newIndex.file < processedData[newIndex.section].data.length - 1) {
            newIndex.file += 1;
          } else if (newIndex.section < processedData.length - 1) {
            if (processedData?.[newIndex.section + 1]?.data?.length > 0) {
              newIndex.section += 1;
              newIndex.file = 0;
            }
          }
          return newIndex;
        });
      } else if (event.key === "ArrowUp") {
        setSelectedIndex((prevIndex) => {
          const newIndex = { ...prevIndex };
          if (!processedData?.[newIndex.section]?.data) {
            return newIndex;
          }
          if (newIndex.file > 0) {
            newIndex.file -= 1;
          } else if (newIndex.section > 0) {
            if (processedData?.[newIndex.section - 1]?.data?.length > 0) {
              newIndex.section -= 1;
              newIndex.file = processedData[newIndex.section].data.length - 1;
            }
          }
          return newIndex;
        });
      }
    };

    // 如果正在重置选择，跳过选择逻辑
    if (isResettingSelection) {
      window.addEventListener("keydown", handleKeyDown);
      return () => {
        window.removeEventListener("keydown", handleKeyDown);
      };
    }

    // 只在数据为空或只有一项时重置选择
    if ((!processedData || processedData.length <= 1) && !flatMode) {
      setSelectedIndex({ section: 0, file: 0 });
    } else {
      // 检查当前选择的文件是否在新数据中存在
      const currentSelectedFile = selectedFile;
      if (currentSelectedFile) {
        let fileFound = false;
        processedData.forEach((section, sectionIndex) => {
          // 确保 section 和 section.data 存在
          if (section?.data) {
            const fileIndex = section.data.findIndex(
              (item) => item?.path === currentSelectedFile.path
            );
            if (fileIndex !== -1) {
              fileFound = true;
              setSelectedIndex({ section: sectionIndex, file: fileIndex });
            }
          }
        });

        // 只有在当前选择的文件不存在于新数据中时才重置选择
        if (!fileFound) {
          // 找到第一个有效的section和file
          let foundValidSection = false;
          for (let i = 0; i < processedData.length; i++) {
            if (processedData[i]?.data && processedData[i].data.length > 0) {
              if (
                i === 0 &&
                processedData[0].dataType === DataType.SearchOnWeb &&
                processedData.length > 1
              ) {
                continue; // 如果第一个是网络搜索并且有后续section，则跳过
              }
              setSelectedIndex({ section: i, file: 0 });
              foundValidSection = true;
              break;
            }
          }

          // 如果没有找到有效section，则设置为默认值
          if (!foundValidSection) {
            setSelectedIndex({ section: 0, file: 0 });
          }
        }
      } else {
        // 如果没有选中的文件，则设置默认选择
        if (
          processedData[0]?.dataType === DataType.SearchOnWeb &&
          processedData.length > 1
        ) {
          // 确保 section 1 存在且有数据
          if (processedData[1]?.data && processedData[1].data.length > 0) {
            setSelectedIndex({ section: 1, file: 0 });
          } else {
            // 找到第一个有效的section
            let foundValidSection = false;
            for (let i = 0; i < processedData.length; i++) {
              if (processedData[i]?.data && processedData[i].data.length > 0) {
                setSelectedIndex({ section: i, file: 0 });
                foundValidSection = true;
                break;
              }
            }

            // 如果没有找到有效section，则设置为默认值
            if (!foundValidSection) {
              setSelectedIndex({ section: 0, file: 0 });
            }
          }
        } else {
          setSelectedIndex({ section: 0, file: 0 });
        }
      }
    }

    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [processedData, isResettingSelection]);

  useEffect(() => {
    if (processedData && processedData.length > 0) {
      // 使用可选链安全获取当前文件
      const selectedFile =
        processedData?.[selectedIndex.section]?.data?.[selectedIndex.file];
      setSelectedFile(selectedFile);
      onSelect(selectedFile);
      if (selectedFileRef.current && !isResettingSelection) {
        // 使用 setTimeout 确保在 DOM 更新后再执行滚动操作
        setTimeout(() => {
          if (selectedFileRef.current) {
            selectedFileRef.current.scrollIntoView({
              behavior: lastAction == "keyboard" ? "auto" : "smooth",
              block: "nearest",
            });
          }
        }, 0);
      }
    }
  }, [
    selectedIndex,
    onSelect,
    lastAction,
    isRightPanelExpanded,
    processedData,
    isResettingSelection,
  ]);

  useEffect(() => {
    if (
      processedData &&
      processedData.length > 0 &&
      (selectedIndex.section === 0 || selectedIndex.section === 1) &&
      selectedIndex.file === 0 &&
      !isResettingSelection
    ) {
      // 平滑滚动到顶端
      if (containerRef.current) {
        containerRef.current.scrollTo({
          top: 0,
          behavior: "smooth",
        });
      }
    }
  }, [processedData, isResettingSelection]);

  // 监听来自MainInput的滚动事件
  useEffect(() => {
    const handleScrollToSection = (event: CustomEvent) => {
      const { sectionIndex, selectFirstFile } = event.detail;
      scrollToSection(sectionIndex);

      // 如果需要选择第一个文件
      if (
        selectFirstFile &&
        processedData &&
        processedData[sectionIndex] &&
        processedData[sectionIndex].data.length > 0
      ) {
        const firstFile = processedData[sectionIndex].data[0];
        setSelectedFile(firstFile);
        onSelect(firstFile);
        setSelectedIndex({ section: sectionIndex, file: 0 });
      }
    };

    window.addEventListener("scrollToSection", handleScrollToSection);
    return () => {
      window.removeEventListener("scrollToSection", handleScrollToSection);
    };
  }, [processedData, onSelect]);

  // 当排序类型或平铺模式改变时，重置选择到最开头的文件
  useEffect(() => {
    if (processedData && processedData.length > 0) {
      // 设置重置标志位
      setIsResettingSelection(true);

      // 找到第一个有效的section和文件
      let foundValidSelection = false;
      for (let i = 0; i < processedData.length; i++) {
        if (processedData[i]?.data && processedData[i].data.length > 0) {
          // 如果第一个是网络搜索且有其他section，跳过网络搜索
          if (
            i === 0 &&
            processedData[0].dataType === DataType.SearchOnWeb &&
            processedData.length > 1
          ) {
            continue;
          }

          const firstFile = processedData[i].data[0];
          setSelectedFile(firstFile);
          onSelect(firstFile);
          setSelectedIndex({ section: i, file: 0 });
          foundValidSelection = true;
          break;
        }
      }

      // 如果没有找到有效选择，设置为默认值
      if (!foundValidSelection) {
        setSelectedIndex({ section: 0, file: 0 });
      }

      // 重置标志位
      setTimeout(() => {
        setIsResettingSelection(false);
      }, 100);
    }
  }, [flatMode, sortBy, sortOrder]);

  useEffect(() => {
    if (
      processedData?.length > 1 &&
      processedData[0]?.dataType === DataType.SearchOnWeb
    ) {
      // 确保 section 1 存在且有数据
      if (processedData[1]?.data && processedData[1].data.length > 0) {
        setSelectedIndex({ section: 1, file: 0 });
      } else {
        // 找到第一个有效的section
        let foundValidSection = false;
        for (let i = 0; i < processedData.length; i++) {
          if (processedData[i]?.data && processedData[i].data.length > 0) {
            setSelectedIndex({ section: i, file: 0 });
            foundValidSection = true;
            break;
          }
        }

        // 如果没有找到有效section，则设置为默认值
        if (!foundValidSection) {
          setSelectedIndex({ section: 0, file: 0 });
        }
      }
    } else {
      setSelectedIndex({ section: 0, file: 0 });
    }
  }, [inputValue]);

  // 当搜索任务变化时，清除缓存的高亮数据和请求记录
  useEffect(() => {
    setAsyncHighlightData({});
    fileItemRefs.current.clear();
  }, [searchUuid]);



  // 当数据变化时，重新设置观察者
  useEffect(() => {
    // 清理旧的观察者和引用
    fileItemRefs.current.clear();
  }, [processedData]);

  // 监听滚动事件，检测用户手动滚动
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleScroll = () => {
      // 如果是程序触发的滚动，不做处理
      if (isScrolling) return;

      // 用户手动滚动，重置滚动状态
      if (scrollTimeoutRef.current !== null) {
        clearTimeout(scrollTimeoutRef.current);
        scrollTimeoutRef.current = null;
      }
      setIsScrolling(false);
    };

    container.addEventListener("scroll", handleScroll);
    return () => {
      container.removeEventListener("scroll", handleScroll);
    };
  }, [isScrolling]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current !== null) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  if (!processedData || processedData.length === 0) {
    return null;
  }

  const onItemClick = async (fileType: DataType, file: Data) => {
    // 确保 file 存在
    if (!file) return;

    const appConfig = await readAppConfig();
    const urlTemplate = appConfig?.default_search_engine
      ?.url_template as string;
    switch (fileType) {
      case DataType.SearchOnWeb:
        if (urlTemplate) {
          open(urlTemplate.replace("%s", inputValue));
        }
        break;
      case DataType.UwpApp:
        // 对于 UWP 应用，可以尝试通过协议启动
        if (file.appUserModelId) {
          try {
            await runUwp(file.appUserModelId);
          } catch (error) {
            console.error("Failed to launch UWP app:", error);
          }
        }
        break;
      default:
        if (file.path) {
          addCache(file.path);
          openFileWithoutAdmin(file.path);
        }
        break;
    }
    getCurrentWindow().hide();
  };

  const handleMouseMove = (
    file: Data,
    sectionIndex: number,
    fileIndex: number
  ) => {
    // 如果正在滚动，不响应鼠标移动事件
    if (isScrolling) return;

    setLastAction("mouse");
    setSelectedFile(file);
    onSelect(file);
    setSelectedIndex({
      section: sectionIndex,
      file: fileIndex,
    });
  };

  return (
    <>
      <CustomScrollbar
        className="w-full h-answer"
        normalWidth={6}
        expandedWidth={14}
        animationDuration={300}
        thumbColor="rgba(100, 100, 100, 0.5)"
        thumbHoverColor="rgba(100, 100, 100, 0.8)"
        contentRef={containerRef}
      >
        <ul
          ref={containerRef}
          className="w-full p-4 bg-main-query dark:bg-muted transition-opacity"
          style={{
            background: "transparent",
          }}
        >
          {processedData?.map(
            (section, sectionIndex) =>
              section && (
                <li
                  key={sectionIndex}
                  className="mb-4"
                  ref={sectionRefs.current[sectionIndex]}
                >
                  {/* 在平铺模式下不显示section标题 */}
                  {!flatMode && (
                    <h2 className="font-bold mb-2 border-b border-b-seperate-line dark:border-gray-700 text-sm opacity-80">
                      {t(`dataTypes.${section.datatype}`) || section.datatype}
                    </h2>
                  )}
                  <ul className="list-none m-0 p-0">
                    {section.data?.map(
                      (file: Data, fileIndex) =>
                        file && (
                          <li
                            key={file.path + file.metaData.version || fileIndex}
                            ref={(element) => {
                              // 处理选中文件的 ref
                              if (selectedFile === file) {
                                selectedFileRef.current = element;
                              }
                            }}
                            className={`flex items-center p-2 rounded cursor-pointer scroll-m-4 w-full transition-all duration-300 ease-in-out ${
                              selectedFile === file
                                ? "bg-answer dark:bg-gray-700 transform scale-[1.02]"
                                : ""
                            }`}
                            onMouseMove={() =>
                              handleMouseMove(file, sectionIndex, fileIndex)
                            }
                            onClick={() => onItemClick(file.type, file)}
                            onContextMenu={(e) => handleContextMenu(e, file)}
                          >
                            <FileListItem
                              file={file}
                              t={t}
                              isRightPanelExpanded={isRightPanelExpanded}
                              searchUuid={searchUuid}
                              onHighlightDataLoaded={(
                                filePath: string,
                                highlightData: any
                              ) => {
                                setAsyncHighlightData((prev) => ({
                                  ...prev,
                                  [filePath]: highlightData,
                                }));
                              }}
                            />
                          </li>
                        )
                    )}
                  </ul>
                </li>
              )
          )}
        </ul>
      </CustomScrollbar>

      {/* 右键菜单 */}
      {contextMenu.visible && contextMenu.file && (
        <div
          className="fixed z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg py-1 min-w-[180px]"
          style={{
            left: contextMenu.x,
            top: contextMenu.y,
            boxShadow:
              "0 8px 32px rgba(0, 0, 0, 0.3), 0 2px 8px rgba(0, 0, 0, 0.2)",
          }}
        >
          {/* 打开文件 - 对所有类型都显示 */}
          <div
            className="px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer flex items-center justify-between"
            onClick={() => handleContextMenuAction("open", contextMenu.file!)}
          >
            <span>{t("searchBar.openFile")}</span>
            <div className="flex items-center gap-1">
              <span className="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs font-mono">
                Enter
              </span>
            </div>
          </div>

          {/* 复制路径 - 对所有类型都显示 */}
          <div
            className="px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer flex items-center justify-between"
            onClick={() => handleContextMenuAction("copy", contextMenu.file!)}
          >
            <span>{t("searchBar.copyPath")}</span>
            <div className="flex items-center gap-1">
              <span className="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs font-mono">
                Ctrl
              </span>
              <span className="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs font-mono">
                C
              </span>
            </div>
          </div>

          {/* 只对非UWP应用显示以下选项 */}
          {contextMenu.file.type !== DataType.UwpApp && (
            <>
              <div
                className="px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer flex items-center justify-between"
                onClick={() =>
                  handleContextMenuAction("openParent", contextMenu.file!)
                }
              >
                <span>{t("searchBar.openParentDirectory")}</span>
                <div className="flex items-center gap-1">
                  <span className="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs font-mono">
                    Ctrl
                  </span>
                  <span className="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs font-mono">
                    P
                  </span>
                </div>
              </div>
              <div
                className="px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer flex items-center justify-between"
                onClick={() =>
                  handleContextMenuAction("openAdmin", contextMenu.file!)
                }
              >
                <span>{t("searchBar.openWithAdmin")}</span>
                <div className="flex items-center gap-1">
                  <span className="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs font-mono">
                    Ctrl
                  </span>
                  <span className="px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs font-mono">
                    O
                  </span>
                </div>
              </div>
            </>
          )}
        </div>
      )}
    </>
  );
};

export default FileList;
