import { invoke } from "@tauri-apps/api/core";
const AiverythingApi = {
  status: "status",
  prepareSearch: "prepare_search",
  searchAsync: "search_async",
  stopSearch: "stop_search",
  searchAI: "search_ai",
  summarizeAI: "summarize_ai",
  removeSummary: "remove_summary",
  getSummary: "get_summary",
  getResult: "get_result",
  getUwpResult: "get_uwp_result",
  removeResult: "remove_result",
  flushFileChanges: "flush_file_changes",
  addCache: "add_cache",
  getCache: "get_cache",
  deleteCache: "delete_cache",
  frequentResult: "get_frequent_result",
  icon: "get_icon",
  update: "update",
  close: "close",
  getConfig: "get_config",
  setConfig: "set_config",
  getDisk: "get_disk",
  getGpu: "get_gpu",
  readAppConfig: "read_app_config",
  setAppConfig: "set_app_config",
  preExit: "pre_exit",
  getCalculatedWindowInfo: "get_calculated_window_info",
  setExplorerEditPath: "set_explorer_edit_path",
  bringWindowToTop: "bring_window_to_top",
  isShiftDoubleClicked: "is_shift_key_double_clicked",
  isCtrlDoubleClicked: "is_ctrl_key_double_clicked",
  getWindowAcrylicState: "get_window_acrylic_state",
  applyWindowAcrylic: "apply_window_acrylic",
  clearWindowAcrylic: "clear_window_acrylic",
  getAsyncKeyState: "get_async_key_state",
  getSwapButtonState: "get_swap_button_state",
  runUwp: "run_uwp",
  getCurrentCorePort: "get_current_core_port",
};

const baseUrl = "https://localhost";

function getCurrentCorePort() {
  return invoke<any>(AiverythingApi.getCurrentCorePort);
}

export function status() {
  return invoke<any>(AiverythingApi.status);
}

export function prepareSearch(searchText: string, maxResultNum: number) {
  return invoke<any>(AiverythingApi.prepareSearch, {
    searchText,
    maxResultNum,
  });
}

export function searchAsync(searchText: string, maxResultNum: number) {
  return invoke<any>(AiverythingApi.searchAsync, {
    searchText,
    maxResultNum,
  });
}

export function stopSearch(uuid: string) {
  if (!uuid) {
    uuid = "";
  }
  return invoke<any>(AiverythingApi.stopSearch, {
    uuid,
  });
}

export function removeSummary(sessionId: string) {
  return invoke<any>(AiverythingApi.removeSummary, {
    sessionId,
  });
}

export async function getSummaryStream(
  sessionId: string
): Promise<EventSource> {
  const port = await getCurrentCorePort();
  const url = `${baseUrl}:${port}/summaryStream?sessionId=${sessionId}`;

  // 创建 EventSource 并添加 withCredentials 选项
  return new EventSource(url, { withCredentials: false });
}

export function getSummary(sessionId: string) {
  return invoke<any>(AiverythingApi.getSummary, {
    sessionId,
  });
}

export function summarizeAI(file: string) {
  return invoke<any>(AiverythingApi.summarizeAI, {
    file,
  });
}

export function searchAI(searchText: string, maxResultNum: number) {
  return invoke<any>(AiverythingApi.searchAI, {
    searchText,
    maxResultNum,
  });
}

export function getResult(uuid: string) {
  return invoke<any>(AiverythingApi.getResult, {
    uuid,
  });
}

export function getUwpResult(uuid: string) {
  return invoke<any>(AiverythingApi.getUwpResult, {
    uuid,
  });
}

export function removeResult(uuid: string) {
  return invoke<any>(AiverythingApi.removeResult, {
    uuid,
  });
}

export function flushFileChanges() {
  return invoke<any>(AiverythingApi.flushFileChanges);
}

export function addCache(path: string) {
  return invoke<any>(AiverythingApi.addCache, {
    path,
  });
}

export function getCache() {
  return invoke<any>(AiverythingApi.getCache);
}

export function deleteCache(path: string) {
  return invoke<any>(AiverythingApi.deleteCache, {
    path,
  });
}

export function getFrequentResult(num: number, searchText: string) {
  return invoke<any>(AiverythingApi.frequentResult, {
    num,
    searchText,
  });
}

export function getIcon(path: string, isUwp: boolean) {
  return invoke<any>(AiverythingApi.icon, {
    path,
    isUwp,
  });
}

export function update(isDropPrevious: boolean) {
  return invoke<any>(AiverythingApi.update, {
    isDropPrevious,
  });
}

export function close() {
  return invoke<any>(AiverythingApi.close);
}

export function getConfig() {
  return invoke<any>(AiverythingApi.getConfig);
}

export function setConfig(config: any) {
  return invoke<any>(AiverythingApi.setConfig, {
    config,
  });
}

export function getDisk() {
  return invoke<any>(AiverythingApi.getDisk);
}

export function getGpu() {
  return invoke<any>(AiverythingApi.getGpu);
}

export function readAppConfig() {
  return invoke<any>(AiverythingApi.readAppConfig);
}

export function setAppConfig(settings: any) {
  return invoke<any>(AiverythingApi.setAppConfig, {
    settings,
  });
}

export function preExit() {
  return invoke<any>(AiverythingApi.preExit);
}

export function getCalculatedWindowInfo() {
  return invoke<any>(AiverythingApi.getCalculatedWindowInfo);
}

export function setExplorerEditPath(path: string, fileName: string) {
  return invoke<any>(AiverythingApi.setExplorerEditPath, {
    path,
    fileName,
  });
}

export function bringWindowToTop() {
  return invoke<any>(AiverythingApi.bringWindowToTop);
}

export function isCtrlDoubleClicked() {
  return invoke<any>(AiverythingApi.isCtrlDoubleClicked);
}

export function isShiftDoubleClicked() {
  return invoke<any>(AiverythingApi.isShiftDoubleClicked);
}

export function getWindowAcrylicState() {
  return invoke<any>(AiverythingApi.getWindowAcrylicState);
}

export function applyWindowAcrylic() {
  return invoke<any>(AiverythingApi.applyWindowAcrylic);
}

export function clearWindowAcrylic() {
  return invoke<any>(AiverythingApi.clearWindowAcrylic);
}

export function getAsyncKeyState(key: number) {
  return invoke<any>(AiverythingApi.getAsyncKeyState, {
    key,
  });
}

export function getSwapButtonState() {
  return invoke<any>(AiverythingApi.getSwapButtonState);
}

export function runUwp(appUserModelId: string) {
  return invoke<any>(AiverythingApi.runUwp, {
    appUserModelId,
  });
}

export async function highlight(
  fileName: string,
  parentPath: string,
  uuid: string
) {
  const port = await getCurrentCorePort();
  const url = `${baseUrl}:${port}/highlight?fileName=${fileName}&parentPath=${parentPath}&uuid=${uuid}`;
  return fetch(url, {
    method: "GET",
  });
}
